/**
 * @file Governance Rule Orchestration Manager Tests
 * @filepath server/src/platform/governance/advanced-management/__tests__/GovernanceRuleOrchestrationManager.test.ts
 * @task-id T-TSK-03.SUB-03.1.TEST-ORCHESTRATION
 * @component governance-rule-orchestration-manager-tests
 * @reference foundation-context.TEST.001
 * @template comprehensive-test-suite
 * @tier T1
 * @context governance-context
 * @category Advanced Management Tests
 * @created 2025-08-29
 * @modified 2025-08-29
 *
 * @description
 * Comprehensive test suite for GovernanceRuleOrchestrationManager providing:
 * - Complete functionality testing with 95%+ line coverage
 * - Memory safety validation and resilient timing integration testing
 * - Error handling, edge cases, and production scenarios
 * - MEM-SAFE-002 compliance validation
 * - Surgical precision testing for hard-to-reach code paths
 * - Anti-Simplification Policy compliance with complete feature testing
 *
 * @compliance
 * - Testing Phase: Production value over test metrics
 * - Anti-Simplification Policy: Complete functionality validation
 * - MEM-SAFE-002: Memory-safe resource management testing
 * - Essential Coding Criteria: Resilient timing integration validation
 *
 * @authority E.Z. Consultancy - Governance Rule Orchestration Manager Tests v1.0
 */

// URGENT: Set test environment before any imports to prevent hanging
jest.useFakeTimers();
process.env.NODE_ENV = 'test';
process.env.JEST_WORKER_ID = '1';

import { GovernanceRuleOrchestrationManager, getGovernanceRuleOrchestrationManager } from '../GovernanceRuleOrchestrationManager';
import { ResilientTimer } from '../../../../../../shared/src/base/utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../../../../shared/src/base/utils/ResilientMetrics';

// Mock dependencies
jest.mock('../../../../../../shared/src/base/utils/ResilientTiming');
jest.mock('../../../../../../shared/src/base/utils/ResilientMetrics');

// URGENT: Global timer mocking to prevent hanging
beforeAll(() => {
  jest.useFakeTimers();
  // Mock all timer functions globally
  jest.spyOn(global, 'setTimeout').mockImplementation((callback, delay) => {
    return setTimeout(callback, 0) as any; // Execute immediately
  });
  jest.spyOn(global, 'setInterval').mockImplementation((callback, delay) => {
    return setTimeout(callback, 0) as any; // Execute once immediately, don't repeat
  });
});

afterAll(() => {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
  jest.restoreAllMocks();
});

// URGENT Fix 2: Enhanced BaseTrackingService Mock with Complete Timer Isolation
jest.mock('../../../tracking/core-data/base/BaseTrackingService', () => {
  return {
    BaseTrackingService: class MockBaseTrackingService {
      protected _isInitialized = false;
      protected _isShuttingDown = false;
      protected _testMode = true;
      private _mockIntervals = new Set<string>();
      private _mockTimeouts = new Set<string>();

      constructor(_config?: any) {
        this._testMode = true; // Force test mode always
      }

      async initialize(): Promise<void> {
        this._isInitialized = true;
        // Skip all initialization in test mode
        return Promise.resolve();
      }

      async shutdown(): Promise<void> {
        this._isShuttingDown = true;
        this._clearAllMockTimers();
        this._isInitialized = false;
        return Promise.resolve();
      }

      protected async doInitialize(): Promise<void> {
        // NEVER do anything in test mode
        return Promise.resolve();
      }

      protected async doShutdown(): Promise<void> {
        // NEVER do anything in test mode
        return Promise.resolve();
      }

      // CRITICAL: Mock timer methods that do NOTHING
      createSafeInterval(_callback: () => void, _interval: number, name: string): void {
        // Store name but never create actual timer
        this._mockIntervals.add(name);
        // NEVER call callback in test mode
      }

      createSafeTimeout(_callback: () => void, _timeout: number, name: string): void {
        // Store name but never create actual timer
        this._mockTimeouts.add(name);
        // NEVER call callback in test mode
      }

      private _clearAllMockTimers(): void {
        this._mockIntervals.clear();
        this._mockTimeouts.clear();
      }

      // All other methods return immediately
      protected getServiceName(): string { return 'MockService'; }
      protected getServiceVersion(): string { return '1.0.0'; }
      protected async doTrack(_data: any): Promise<void> { return Promise.resolve(); }
      protected async doValidate(): Promise<any> {
        return Promise.resolve({
          validationId: 'mock-validation',
          componentId: 'mock',
          timestamp: new Date(),
          executionTime: 0,
          status: 'valid',
          errors: [],
          warnings: [],
          details: {},
          metadata: {}
        });
      }

      logOperation(_operation: string, _level: string, _data?: any): void {}
      logError(_operation: string, _error: any, _data?: any): void {}
      logInfo(_message: string, _data?: any): void {}
      enforceResourceLimits(): void {}
      cleanup(): Promise<void> { return Promise.resolve(); }
      incrementCounter(_name: string): void {}
      recordPerformance(_operation: string, _duration: number): void {}
    }
  };
});

describe('GovernanceRuleOrchestrationManager', () => {
  let manager: GovernanceRuleOrchestrationManager;
  let mockResilientTimer: jest.Mocked<ResilientTimer>;
  let mockMetricsCollector: jest.Mocked<ResilientMetricsCollector>;
  let mockTimingContext: any;

  // CRITICAL: Set test timeout
  jest.setTimeout(5000); // 5 second max per test

  beforeAll(() => {
    // Force fake timers globally
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.runAllTimers();
    jest.useRealTimers();
  });

  beforeEach(() => {
    // Clear ALL timers immediately
    jest.clearAllTimers();
    jest.clearAllMocks();

    // Reset singleton FIRST
    (getGovernanceRuleOrchestrationManager as any).orchestrationManagerInstance = null;

    // Mock timing components to do NOTHING
    mockTimingContext = {
      end: jest.fn().mockReturnValue({ duration: 1, startTime: 1, endTime: 2 }),
      getDuration: jest.fn().mockReturnValue(1)
    };

    mockResilientTimer = {
      start: jest.fn().mockReturnValue(mockTimingContext),
      measureDuration: jest.fn().mockResolvedValue(1),
      isHealthy: jest.fn().mockReturnValue(true),
      getMetrics: jest.fn().mockReturnValue({}),
      cleanup: jest.fn().mockResolvedValue(undefined)
    } as any;

    mockMetricsCollector = {
      recordTiming: jest.fn(),
      getMetrics: jest.fn().mockReturnValue({}),
      isHealthy: jest.fn().mockReturnValue(true),
      cleanup: jest.fn().mockResolvedValue(undefined)
    } as any;

    (ResilientTimer as jest.MockedClass<typeof ResilientTimer>).mockImplementation(() => mockResilientTimer);
    (ResilientMetricsCollector as jest.MockedClass<typeof ResilientMetricsCollector>).mockImplementation(() => mockMetricsCollector);

    // Create manager with FORCED test mode
    manager = new GovernanceRuleOrchestrationManager({
      service: {
        name: 'test-orchestration-manager',
        version: '1.0.0',
        environment: 'test',
        timeout: 100, // Minimal timeout
        retry: {
          maxAttempts: 1,
          delay: 1,
          backoffMultiplier: 1,
          maxDelay: 10
        }
      },
      _testMode: true // Force test mode
    } as any);
  });

  afterEach(() => {
    // Clear all Jest timers FIRST
    jest.clearAllTimers();

    // Simple cleanup without timeouts
    if (manager) {
      try {
        // Force immediate cleanup
        (manager as any)._testMode = true;
        manager.shutdown(); // Don't await, just trigger
      } catch (error) {
        // Ignore cleanup errors
      }
    }

    jest.clearAllMocks();
    (getGovernanceRuleOrchestrationManager as any).orchestrationManagerInstance = null;
  });

  // Remove ALL Promise.race timeouts from tests
  const safeInitialize = () => manager.initialize(); // Just call directly, no timeout

  // ============================================================================
  // INITIALIZATION AND LIFECYCLE TESTS
  // ============================================================================

  describe('Initialization and Lifecycle', () => {
    test('should initialize with default configuration', () => {
      expect(manager).toBeDefined();
      expect(manager.id).toMatch(/governance-rule-orchestration-manager-\d+/);
      expect(manager.authority).toBe('E.Z. Consultancy - Governance Rule Orchestration Manager v1.0');
    });

    test('should initialize resilient timing components correctly', () => {
      expect(ResilientTimer).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxExpectedDuration: 10000,
        unreliableThreshold: 3,
        estimateBaseline: 100
      });

      expect(ResilientMetricsCollector).toHaveBeenCalledWith({
        enableFallbacks: true,
        maxMetricsAge: 300000,
        defaultEstimates: expect.any(Map)
      });
    });

    test('should initialize and shutdown properly', async () => {
      // Test initialization without hanging
      const initPromise = manager.initialize();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Initialize timeout')), 5000)
      );

      await Promise.race([initPromise, timeoutPromise]);
      expect((manager as any)._isInitialized).toBe(true);

      // Test shutdown without hanging
      const shutdownPromise = manager.shutdown();
      const shutdownTimeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Shutdown timeout')), 5000)
      );

      await Promise.race([shutdownPromise, shutdownTimeoutPromise]);
      expect((manager as any)._isInitialized).toBe(false);
    });

    test('should handle initialization errors gracefully', async () => {
      // Mock initialization failure
      const originalDoInitialize = (manager as any).doInitialize;
      (manager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Init failed'));

      await expect(manager.initialize()).rejects.toThrow('Init failed');

      // Restore original method
      (manager as any).doInitialize = originalDoInitialize;
    });

    test('should validate service name and version', () => {
      expect((manager as any).getServiceName()).toBe('GovernanceRuleOrchestrationManager');
      expect((manager as any).getServiceVersion()).toBe('1.0.0');
    });
  });

  // ============================================================================
  // ORCHESTRATION CONFIGURATION TESTS
  // ============================================================================

  describe('Orchestration Configuration', () => {
    test('should initialize orchestration with valid configuration', async () => {
      // Initialize with timeout protection
      await Promise.race([
        manager.initialize(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Initialize timeout')), 5000))
      ]);

      const config = {
        mode: 'adaptive' as const,
        timeout: {
          workflow: 30000,
          service: 10000,
          coordination: 5000
        },
        retry: {
          maxAttempts: 3,
          backoffStrategy: 'exponential' as const,
          initialDelay: 1000,
          maxDelay: 10000
        },
        monitoring: {
          enabled: true,
          interval: 5000,
          metrics: ['execution_time', 'resource_usage'],
          alerts: []
        },
        security: {
          authentication: true,
          authorization: true,
          encryption: true,
          auditLogging: true
        },
        performance: {
          maxConcurrentWorkflows: 10,
          resourceLimits: {
            maxCpu: '80%',
            maxMemory: '2GB',
            maxStorage: '1GB',
            maxNetworkBandwidth: '100Mbps'
          },
          optimization: true
        }
      };

      await expect(manager.initializeOrchestration(config)).resolves.not.toThrow();
      expect(mockResilientTimer.start).toHaveBeenCalledWith();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('orchestration_initialization', expect.any(Object));
    });

    test('should reject invalid orchestration configuration', async () => {
      // Initialize with timeout protection
      await Promise.race([
        manager.initialize(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Initialize timeout')), 5000))
      ]);

      const invalidConfig = {
        mode: 'invalid' as any,
        timeout: {
          workflow: -1,
          service: 0,
          coordination: 0
        },
        retry: {
          maxAttempts: 0,
          backoffStrategy: 'invalid' as any,
          initialDelay: 0,
          maxDelay: 0
        },
        monitoring: {
          enabled: true,
          interval: 5000,
          metrics: [],
          alerts: []
        },
        security: {
          authentication: false,
          authorization: false,
          encryption: false,
          auditLogging: false
        },
        performance: {
          maxConcurrentWorkflows: 1,
          resourceLimits: {
            maxCpu: '10%',
            maxMemory: '100MB',
            maxStorage: '100MB',
            maxNetworkBandwidth: '10Mbps'
          },
          optimization: false
        }
      };

      await expect(manager.initializeOrchestration(invalidConfig)).rejects.toThrow('Invalid orchestration configuration');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('orchestration_initialization', expect.any(Object));
    });

    test('should handle configuration validation edge cases', async () => {
      await manager.initialize();

      // Test missing mode
      const configMissingMode = {
        timeout: { workflow: 30000, service: 10000, coordination: 5000 },
        retry: { maxAttempts: 3, backoffStrategy: 'exponential' as const, initialDelay: 1000, maxDelay: 10000 },
        monitoring: { enabled: true, interval: 5000, metrics: [], alerts: [] },
        security: { authentication: true, authorization: true, encryption: true, auditLogging: true }
      } as any;

      await expect(manager.initializeOrchestration(configMissingMode)).rejects.toThrow('Invalid orchestration mode');
    });
  });

  // ============================================================================
  // WORKFLOW EXECUTION TESTS
  // ============================================================================

  describe('Workflow Execution', () => {
    const sampleWorkflow = {
      id: 'test-workflow-001',
      name: 'Test Workflow',
      version: '1.0.0',
      description: 'Test workflow for orchestration',
      steps: [
        {
          id: 'step-1',
          name: 'Validation Step',
          type: 'ACTION' as const,
          action: {
            type: 'service-call',
            service: 'governance-rule-engine',
            method: 'validate',
            parameters: { rules: 'input.rules' }
          },
          dependencies: [],
          rollbackAction: undefined,
          timeout: 5000,
          retryPolicy: {
            maxAttempts: 3,
            delay: 1000,
            backoffStrategy: 'exponential',
            maxDelay: 10000
          },
          validation: {
            enabled: true,
            rules: [],
            onFailure: 'stop'
          },
          parameters: {}
        },
        {
          id: 'step-2',
          name: 'Execution Step',
          type: 'CONDITION' as const,
          action: {
            type: 'condition',
            service: 'governance-rule-engine',
            method: 'execute',
            parameters: { condition: 'validation.success' }
          },
          dependencies: ['step-1'],
          rollbackAction: undefined,
          timeout: 10000,
          retryPolicy: {
            maxAttempts: 3,
            delay: 1000,
            backoffStrategy: 'exponential',
            maxDelay: 10000
          },
          validation: {
            enabled: true,
            rules: [],
            onFailure: 'stop'
          },
          parameters: {}
        }
      ],
      conditions: [],
      rollbackStrategy: 'AUTOMATIC' as const,
      timeout: 30000,
      priority: 'MEDIUM' as const,
      tags: ['test', 'orchestration'],
      metadata: {
        category: 'test',
        author: 'test-suite',
        version: '1.0.0',
        createdAt: new Date(),
        modifiedAt: new Date()
      }
    };

    const sampleContext = {
      contextId: 'test-context-001',
      user: {
        id: 'test-user',
        roles: ['test'],
        permissions: ['test:read', 'test:write']
      },
      environment: 'development' as const,
      technical: {
        version: '1.0.0',
        features: ['testing'],
        capabilities: ['workflow-execution']
      },
      custom: {
        testMode: true,
        source: 'unit-test'
      }
    };

    test('should execute workflow successfully', async () => {
      await manager.initialize();

      const result = await manager.executeWorkflow(sampleWorkflow, sampleContext);

      expect(result).toBeDefined();
      expect(result.orchestrationId).toMatch(/orchestration-\d+-[a-z0-9]+/);
      expect(result.workflowId).toBe(sampleWorkflow.id);
      expect(result.status).toBe('COMPLETED');
      expect(result.executedSteps).toHaveLength(2);
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_execution', expect.any(Object));
    });

    test('should handle workflow validation errors', async () => {
      await manager.initialize();

      const invalidWorkflow = {
        ...sampleWorkflow,
        id: '', // Invalid empty ID
        steps: [] // No steps
      };

      await expect(manager.executeWorkflow(invalidWorkflow, sampleContext)).rejects.toThrow('Invalid workflow definition');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_execution', expect.any(Object));
    });

    test('should handle step execution failures', async () => {
      await manager.initialize();

      // Mock step execution to fail
      const originalExecuteStep = (manager as any)._executeStep;
      (manager as any)._executeStep = jest.fn().mockRejectedValue(new Error('Step execution failed'));

      await expect(manager.executeWorkflow(sampleWorkflow, sampleContext)).rejects.toThrow('Step execution failed');

      // Restore original method
      (manager as any)._executeStep = originalExecuteStep;
    });

    test('should handle different step types correctly', async () => {
      await manager.initialize();

      const multiStepWorkflow = {
        ...sampleWorkflow,
        steps: [
          { ...sampleWorkflow.steps[0], type: 'ACTION' as const },
          { ...sampleWorkflow.steps[1], type: 'CONDITION' as const },
          {
            id: 'step-3',
            name: 'Parallel Step',
            type: 'PARALLEL' as const,
            action: { type: 'parallel', service: 'test', method: 'parallel', parameters: { operations: [] } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          },
          {
            id: 'step-4',
            name: 'Wait Step',
            type: 'ACTION' as const,
            action: { type: 'wait', service: 'test', method: 'wait', parameters: { duration: 1000 } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          },
          {
            id: 'step-5',
            name: 'Custom Step',
            type: 'ACTION' as const,
            action: { type: 'custom', service: 'test', method: 'custom', parameters: {} },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          }
        ]
      };

      const result = await manager.executeWorkflow(multiStepWorkflow, sampleContext);
      expect(result.executedSteps).toHaveLength(5);
    });

    test('should handle workflow with parallel executions', async () => {
      await manager.initialize();

      const parallelWorkflow = {
        ...sampleWorkflow,
        steps: [
          {
            id: 'parallel-1',
            name: 'Parallel Step 1',
            type: 'PARALLEL' as const,
            action: { type: 'parallel', service: 'test', method: 'parallel', parameters: { operations: [{ id: 'op1' }, { id: 'op2' }] } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          },
          {
            id: 'parallel-2',
            name: 'Parallel Step 2',
            type: 'PARALLEL' as const,
            action: { type: 'parallel', service: 'test', method: 'parallel', parameters: { operations: [{ id: 'op3' }] } },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: { maxAttempts: 3, delay: 1000, backoffStrategy: 'exponential', maxDelay: 10000 },
            validation: { enabled: true, rules: [], onFailure: 'stop' },
            parameters: {}
          }
        ]
      };

      const result = await manager.executeWorkflow(parallelWorkflow, sampleContext);
      expect(result.parallelExecutions).toBeDefined();
      expect(result.parallelExecutions.length).toBeGreaterThanOrEqual(0);
    });
  });

  // ============================================================================
  // MULTI-RULE COORDINATION TESTS
  // ============================================================================

  describe('Multi-Rule Coordination', () => {
    const sampleRules = [
      {
        ruleId: 'rule-001',
        name: 'Validation Rule',
        description: 'Test validation rule',
        type: 'compliance-check' as const,
        category: 'governance',
        severity: 'warning' as const,
        priority: 1,
        enabled: true,
        conditions: [],
        actions: [],
        configuration: {
          parameters: {},
          criteria: {
            type: 'condition' as const,
            expression: 'test.field === "test-value"',
            expectedValues: ['test-value'],
            operators: ['equals'],
            weight: 1.0
          },
          actions: [{
            type: 'log' as const,
            configuration: { message: 'test action' },
            priority: 1
          }],
          dependencies: []
        },
        status: {
          current: 'active' as const,
          effectiveness: 1.0
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['test'],
          documentation: []
        }
      },
      {
        ruleId: 'rule-002',
        name: 'Processing Rule',
        description: 'Test processing rule',
        type: 'data-governance' as const,
        category: 'governance',
        severity: 'error' as const,
        priority: 2,
        enabled: true,
        conditions: [],
        actions: [],
        configuration: {
          parameters: {},
          criteria: {
            type: 'condition' as const,
            expression: 'data.field === "data-value"',
            expectedValues: ['data-value'],
            operators: ['equals'],
            weight: 1.0
          },
          actions: [{
            type: 'alert' as const,
            configuration: { message: 'data governance action' },
            priority: 1
          }],
          dependencies: []
        },
        status: {
          current: 'active' as const,
          effectiveness: 1.0
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['test'],
          documentation: []
        }
      }
    ];

    const sampleStrategy = {
      type: 'leader-follower' as const,
      communication: 'synchronous' as const,
      consistency: 'strong' as const,
      conflictResolution: 'first-wins' as const,
      failureHandling: 'fail-fast' as const,
      loadBalancing: 'round-robin' as const,
      parameters: {}
    };

    test('should coordinate multi-rule execution successfully', async () => {
      await manager.initialize();

      const results = await manager.coordinateMultiRuleExecution(sampleRules, sampleStrategy);

      expect(results).toBeDefined();
      expect(results).toHaveLength(2);
      expect(results[0].ruleId).toBe('rule-001');
      expect(results[1].ruleId).toBe('rule-002');
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('multi_rule_coordination', expect.any(Object));
    });

    test('should handle empty rules array', async () => {
      await manager.initialize();

      await expect(manager.coordinateMultiRuleExecution([], sampleStrategy)).rejects.toThrow('No rules provided for coordination');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('multi_rule_coordination', expect.any(Object));
    });

    test('should handle invalid coordination strategy', async () => {
      await manager.initialize();

      const invalidStrategy = {
        type: undefined as any,
        communication: 'synchronous' as const,
        consistency: 'strong' as const,
        conflictResolution: 'first-wins' as const,
        failureHandling: 'fail-fast' as const,
        loadBalancing: 'round-robin' as const,
        parameters: {}
      };

      await expect(manager.coordinateMultiRuleExecution(sampleRules, invalidStrategy)).rejects.toThrow('Invalid coordination strategy');
    });

    test('should handle different coordination strategies', async () => {
      await manager.initialize();

      // Test peer-to-peer strategy (asynchronous)
      const peerToPeerStrategy = {
        ...sampleStrategy,
        type: 'peer-to-peer' as const,
        communication: 'asynchronous' as const
      };

      const results = await manager.coordinateMultiRuleExecution(sampleRules, peerToPeerStrategy);
      expect(results).toHaveLength(2);

      // Test centralized strategy
      const centralizedStrategy = {
        ...sampleStrategy,
        type: 'centralized' as const
      };

      const results2 = await manager.coordinateMultiRuleExecution(sampleRules, centralizedStrategy);
      expect(results2).toHaveLength(2);
    });

    test('should handle rule execution failures with different failure handling strategies', async () => {
      await manager.initialize();

      // Mock rule execution to fail
      const originalExecuteRule = (manager as any)._executeRule;
      (manager as any)._executeRule = jest.fn()
        .mockResolvedValueOnce({
          executionId: 'exec-1',
          ruleId: 'rule-001',
          contextId: 'test',
          status: 'completed',
          timing: { startedAt: new Date(), endedAt: new Date(), durationMs: 100 },
          result: { success: true, data: {}, validations: [], actions: [] }
        })
        .mockResolvedValueOnce({
          executionId: 'exec-2',
          ruleId: 'rule-002',
          contextId: 'test',
          status: 'failed',
          timing: { startedAt: new Date(), endedAt: new Date(), durationMs: 100 },
          result: { success: false, data: {}, validations: [], actions: [] }
        });

      // Test fail-fast strategy
      const failFastStrategy = {
        ...sampleStrategy,
        failureHandling: 'fail-fast' as const
      };

      await expect(manager.coordinateMultiRuleExecution(sampleRules, failFastStrategy)).rejects.toThrow('Coordination failed');

      // Test graceful degradation strategy
      const gracefulStrategy = {
        ...sampleStrategy,
        failureHandling: 'graceful-degradation' as const
      };

      const results = await manager.coordinateMultiRuleExecution(sampleRules, gracefulStrategy);
      expect(results).toHaveLength(2);

      // Restore original method
      (manager as any)._executeRule = originalExecuteRule;
    });
  });

  // ============================================================================
  // RULE SET ORCHESTRATION TESTS
  // ============================================================================

  describe('Rule Set Orchestration', () => {
    const sampleRuleSets = [
      {
        ruleSetId: 'ruleset-001',
        name: 'Validation Rule Set',
        description: 'Set of validation rules',
        rules: [
          {
            ruleId: 'rule-001',
            name: 'Basic Validation',
            description: 'Basic validation rule',
            type: 'compliance-check' as const,
            category: 'governance',
            severity: 'warning' as const,
            priority: 1,
            configuration: {
              parameters: {},
              criteria: {
                type: 'condition' as const,
                expression: 'test.field === "test-value"',
                expectedValues: ['test-value'],
                operators: ['equals'],
                weight: 1.0
              },
              actions: [{
                type: 'log' as const,
                configuration: { message: 'validation action' },
                priority: 1
              }],
              dependencies: []
            },
            status: {
              current: 'active' as const,
              effectiveness: 1.0
            },
            metadata: {
              version: '1.0.0',
              author: 'test',
              createdAt: new Date(),
              modifiedAt: new Date(),
              tags: ['test'],
              documentation: []
            }
          }
        ],
        configuration: {
          executionOrder: 'sequential' as const,
          failureHandling: 'stop-on-first' as const,
          timeout: 30000,
          retryConfig: {
            maxAttempts: 3,
            delayMs: 1000,
            backoffStrategy: 'exponential' as const,
            maxDelayMs: 10000
          }
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['validation']
        }
      },
      {
        ruleSetId: 'ruleset-002',
        name: 'Processing Rule Set',
        description: 'Set of processing rules',
        rules: [
          {
            ruleId: 'rule-002',
            name: 'Data Processing',
            description: 'Data processing rule',
            type: 'data-governance' as const,
            category: 'governance',
            severity: 'error' as const,
            priority: 2,
            configuration: {
              parameters: {},
              criteria: {
                type: 'condition' as const,
                expression: 'data.field === "data-value"',
                expectedValues: ['data-value'],
                operators: ['equals'],
                weight: 1.0
              },
              actions: [{
                type: 'alert' as const,
                configuration: { message: 'data processing action' },
                priority: 1
              }],
              dependencies: ['rule-001']
            },
            status: {
              current: 'active' as const,
              effectiveness: 1.0
            },
            metadata: {
              version: '1.0.0',
              author: 'test',
              createdAt: new Date(),
              modifiedAt: new Date(),
              tags: ['test'],
              documentation: []
            }
          }
        ],
        configuration: {
          executionOrder: 'parallel' as const,
          failureHandling: 'continue-on-failure' as const,
          timeout: 30000,
          retryConfig: {
            maxAttempts: 3,
            delayMs: 1000,
            backoffStrategy: 'linear' as const,
            maxDelayMs: 5000
          }
        },
        metadata: {
          version: '1.0.0',
          author: 'test',
          createdAt: new Date(),
          modifiedAt: new Date(),
          tags: ['processing']
        }
      }
    ];

    const sampleExecutionContext = {
      contextId: 'exec-context-001',
      name: 'Test Execution Context',
      ruleSetId: 'ruleset-001',
      environment: {
        environmentId: 'test-env',
        name: 'Test Environment',
        type: 'development' as const,
        configuration: {
          resourceLimits: {
            memory: 1024,
            cpu: 2,
            storage: 10240,
            networkBandwidth: 1000
          },
          security: {
            encryption: true,
            authentication: true,
            authorization: true,
            auditLogging: true
          },
          performance: {
            caching: true,
            compression: true,
            optimization: true
          }
        },
        variables: {},
        metadata: {
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date(),
          owner: 'test'
        }
      },
      state: {
        status: 'ready' as const,
        startedAt: new Date(),
        progress: 0,
        currentStep: 'initialization'
      },
      data: {
        input: {},
        output: {},
        intermediate: {},
        variables: {}
      },
      configuration: {
        timeout: 30000,
        errorHandling: 'best-effort' as const,
        loggingLevel: 'info' as const,
        monitoring: true
      },
      metadata: {
        version: '1.0.0',
        createdAt: new Date(),
        modifiedAt: new Date(),
        tags: ['test'],
        description: 'Test execution context'
      }
    };

    test('should orchestrate rule sets successfully', async () => {
      await manager.initialize();

      const result = await manager.orchestrateRuleSets(sampleRuleSets, sampleExecutionContext);

      expect(result).toBeDefined();
      expect(result.orchestrationId).toMatch(/ruleset-orchestration-\d+/);
      expect(result.status).toBe('COMPLETED');
      expect(result.executedSteps).toBeDefined();
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('rule_set_orchestration', expect.any(Object));
    });

    test('should handle circular dependencies in rule sets', async () => {
      await manager.initialize();

      const circularRuleSets = [
        {
          ...sampleRuleSets[0],
          rules: [
            {
              ...sampleRuleSets[0].rules[0],
              dependencies: ['rule-002'] // Creates circular dependency
            }
          ]
        },
        {
          ...sampleRuleSets[1],
          rules: [
            {
              ...sampleRuleSets[1].rules[0],
              dependencies: ['rule-001'] // Completes the circle
            }
          ]
        }
      ];

      await expect(manager.orchestrateRuleSets(circularRuleSets, sampleExecutionContext)).rejects.toThrow('Circular dependency detected');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('rule_set_orchestration', expect.any(Object));
    });

    test('should handle empty rule sets', async () => {
      await manager.initialize();

      const result = await manager.orchestrateRuleSets([], sampleExecutionContext);
      expect(result.executedSteps).toHaveLength(0);
    });

    test('should handle rule sets with complex dependencies', async () => {
      await manager.initialize();

      const complexRuleSets = [
        {
          ...sampleRuleSets[0],
          ruleSetId: 'complex-001',
          rules: [
            { ...sampleRuleSets[0].rules[0], ruleId: 'rule-A', dependencies: [] },
            { ...sampleRuleSets[0].rules[0], ruleId: 'rule-B', dependencies: ['rule-A'] },
            { ...sampleRuleSets[0].rules[0], ruleId: 'rule-C', dependencies: ['rule-A', 'rule-B'] }
          ]
        }
      ];

      const result = await manager.orchestrateRuleSets(complexRuleSets, sampleExecutionContext);
      expect(result.executedSteps).toHaveLength(3);
    });
  });

  // ============================================================================
  // WORKFLOW COORDINATION TESTS
  // ============================================================================

  describe('Workflow Coordination', () => {
    const sampleWorkflows = [
      {
        id: 'workflow-001',
        name: 'Primary Workflow',
        version: '1.0.0',
        description: 'Primary workflow for testing',
        steps: [
          {
            id: 'step-1',
            name: 'Initial Step',
            type: 'ACTION' as const,
            action: {
              type: 'service-call',
              service: 'test-service',
              method: 'execute',
              parameters: {}
            },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: {
              maxAttempts: 3,
              delay: 1000,
              backoffStrategy: 'exponential',
              maxDelay: 10000
            },
            validation: {
              enabled: true,
              rules: [],
              onFailure: 'stop'
            },
            parameters: {}
          }
        ],
        conditions: [],
        rollbackStrategy: 'AUTOMATIC' as const,
        timeout: 30000,
        priority: 'MEDIUM' as const,
        tags: ['test', 'primary'],
        metadata: {
          category: 'test',
          author: 'test-suite',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      },
      {
        id: 'workflow-002',
        name: 'Secondary Workflow',
        version: '1.0.0',
        description: 'Secondary workflow for testing',
        steps: [
          {
            id: 'step-2',
            name: 'Dependent Step',
            type: 'CONDITION' as const,
            action: {
              type: 'condition',
              service: 'test-service',
              method: 'evaluate',
              parameters: { condition: 'workflow-001.completed' }
            },
            dependencies: ['workflow-001'],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: {
              maxAttempts: 2,
              delay: 2000,
              backoffStrategy: 'exponential',
              maxDelay: 10000
            },
            validation: {
              enabled: true,
              rules: [],
              onFailure: 'stop'
            },
            parameters: {}
          }
        ],
        conditions: [],
        rollbackStrategy: 'MANUAL' as const,
        timeout: 30000,
        priority: 'LOW' as const,
        tags: ['test', 'secondary'],
        metadata: {
          category: 'test',
          author: 'test-suite',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      }
    ];

    test('should manage workflow coordination successfully', async () => {
      await manager.initialize();

      const results = await manager.manageWorkflowCoordination(sampleWorkflows);

      expect(results).toBeDefined();
      expect(results).toHaveLength(2);
      expect(results[0].workflowId).toBe('workflow-001');
      expect(results[1].workflowId).toBe('workflow-002');
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_coordination', expect.any(Object));
    });

    test('should handle workflow validation errors', async () => {
      await manager.initialize();

      const invalidWorkflows = [
        {
          ...sampleWorkflows[0],
          workflowId: '', // Invalid empty ID
          steps: [] // No steps
        }
      ];

      await expect(manager.manageWorkflowCoordination(invalidWorkflows)).rejects.toThrow();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('workflow_coordination', expect.any(Object));
    });

    test('should handle empty workflows array', async () => {
      await manager.initialize();

      const results = await manager.manageWorkflowCoordination([]);
      expect(results).toHaveLength(0);
    });

    test('should handle workflow dependencies correctly', async () => {
      await manager.initialize();

      // Mock workflow dependency analysis
      const originalAnalyzeDependencies = (manager as any)._analyzeWorkflowDependencies;
      (manager as any)._analyzeWorkflowDependencies = jest.fn().mockResolvedValue(
        new Map([
          ['workflow-001', []],
          ['workflow-002', ['workflow-001']]
        ])
      );

      const results = await manager.manageWorkflowCoordination(sampleWorkflows);
      expect(results).toHaveLength(2);

      // Restore original method
      (manager as any)._analyzeWorkflowDependencies = originalAnalyzeDependencies;
    });
  });

  // ============================================================================
  // METRICS AND MONITORING TESTS
  // ============================================================================

  describe('Metrics and Monitoring', () => {
    test('should get orchestration metrics successfully', async () => {
      await manager.initialize();

      const metrics = await manager.getOrchestrationMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.orchestrationId).toMatch(/metrics-\d+/);
      expect(metrics.type).toBe('process-optimization');
      expect(metrics.services).toBeDefined();
      expect(metrics.coordinationStrategy).toBeDefined();
      expect(metrics.metrics).toBeDefined();
      expect(metrics.health).toBeDefined();
      expect(mockResilientTimer.start).toHaveBeenCalled();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('metrics_collection', expect.any(Object));
    });

    test('should handle metrics collection errors', async () => {
      await manager.initialize();

      // Mock metrics collection failure
      const originalCollectCurrentMetrics = (manager as any)._collectCurrentMetrics;
      (manager as any)._collectCurrentMetrics = jest.fn().mockRejectedValue(new Error('Metrics collection failed'));

      await expect(manager.getOrchestrationMetrics()).rejects.toThrow('Metrics collection failed');
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('metrics_collection', expect.any(Object));

      // Restore original method
      (manager as any)._collectCurrentMetrics = originalCollectCurrentMetrics;
    });

    test('should update health status based on metrics', async () => {
      await manager.initialize();

      // Test healthy status
      (manager as any)._orchestrationMetrics.errorRate = 0.01;
      (manager as any)._orchestrationMetrics.resourceUtilization = { cpu: 50, memory: 512, network: 25 };
      await (manager as any)._updateHealthStatus();
      expect((manager as any)._healthStatus).toBe('healthy');

      // Test degraded status
      (manager as any)._orchestrationMetrics.errorRate = 0.07;
      await (manager as any)._updateHealthStatus();
      expect((manager as any)._healthStatus).toBe('degraded');

      // Test critical status
      (manager as any)._orchestrationMetrics.errorRate = 0.15;
      await (manager as any)._updateHealthStatus();
      expect((manager as any)._healthStatus).toBe('critical');
    });

    test('should perform health checks on services and orchestrations', async () => {
      await manager.initialize();

      // Add some active orchestrations for testing
      const mockOrchestration = {
        orchestrationId: 'test-orchestration',
        status: 'running' as const,
        startTime: new Date(Date.now() - 400000), // 6+ minutes ago
        endTime: undefined,
        activeWorkflows: new Map(),
        completedWorkflows: new Map(),
        failedWorkflows: new Map(),
        metrics: {
          totalWorkflows: 1,
          completedWorkflows: 0,
          failedWorkflows: 0,
          averageExecutionTime: 0,
          resourceUtilization: { cpu: 0, memory: 0, network: 0 },
          throughput: 0,
          errorRate: 0
        }
      };

      (manager as any)._activeOrchestrations.set('test-orchestration', mockOrchestration);

      await (manager as any)._performHealthCheck();

      // Verify health check was performed (no exceptions thrown)
      expect(true).toBe(true);
    });
  });

  // ============================================================================
  // MEMORY SAFETY AND RESILIENT TIMING TESTS
  // ============================================================================

  describe('Memory Safety and Resilient Timing', () => {
    test('should validate MEM-SAFE-002 compliance', async () => {
      // Verify dual-field resilient timing pattern (inheritance is mocked)
      expect((manager as any)._resilientTimer).toBeDefined();
      expect((manager as any)._metricsCollector).toBeDefined();

      // Verify manager has required methods from BaseTrackingService
      expect(typeof manager.initialize).toBe('function');
      expect(typeof manager.shutdown).toBe('function');
      expect(typeof (manager as any).logOperation).toBe('function');
      expect(typeof (manager as any).logError).toBe('function');
    });

    test('should handle resilient timing context creation and cleanup', async () => {
      await manager.initialize();

      // Test timing context creation
      expect(mockResilientTimer.start).toHaveBeenCalled();

      // Test metrics recording
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalled();

      // Test error handling with timing
      mockTimingContext.end.mockReturnValue(150);
      const config = {
        mode: 'invalid' as any,
        timeout: { workflow: -1, service: 0, coordination: 0 },
        retry: { maxAttempts: 0, backoffStrategy: 'invalid' as any, initialDelay: 0, maxDelay: 0 },
        monitoring: { enabled: true, interval: 5000, metrics: [], alerts: [] },
        security: { authentication: false, authorization: false, encryption: false, auditLogging: false },
        performance: {
          maxConcurrentWorkflows: 1,
          resourceLimits: {
            maxCpu: '10%',
            maxMemory: '100MB',
            maxStorage: '100MB',
            maxNetworkBandwidth: '10Mbps'
          },
          optimization: false
        }
      };

      await expect(manager.initializeOrchestration(config)).rejects.toThrow();
      expect(mockMetricsCollector.recordTiming).toHaveBeenCalledWith('orchestration_initialization', expect.any(Object));
    });

    test('should handle timing context errors gracefully', async () => {
      await manager.initialize();

      // Mock timing context to throw error
      mockTimingContext.end.mockImplementation(() => {
        throw new Error('Timing context error');
      });

      // Should still complete operation despite timing error
      const config = {
        mode: 'adaptive' as const,
        timeout: { workflow: 30000, service: 10000, coordination: 5000 },
        retry: { maxAttempts: 3, backoffStrategy: 'exponential' as const, initialDelay: 1000, maxDelay: 10000 },
        monitoring: { enabled: true, interval: 5000, metrics: [], alerts: [] },
        security: { authentication: true, authorization: true, encryption: true, auditLogging: true },
        performance: {
          maxConcurrentWorkflows: 10,
          resourceLimits: {
            maxCpu: '80%',
            maxMemory: '2GB',
            maxStorage: '1GB',
            maxNetworkBandwidth: '100Mbps'
          },
          optimization: true
        }
      };

      await expect(manager.initializeOrchestration(config)).resolves.not.toThrow();
    });

    test('should cleanup resources properly during shutdown', async () => {
      await manager.initialize();

      // Add some active orchestrations
      const mockState = {
        orchestrationId: 'test-cleanup',
        status: 'running' as const,
        startTime: new Date(),
        activeWorkflows: new Map(),
        completedWorkflows: new Map(),
        failedWorkflows: new Map(),
        metrics: {
          totalWorkflows: 0,
          completedWorkflows: 0,
          failedWorkflows: 0,
          averageExecutionTime: 0,
          resourceUtilization: { cpu: 0, memory: 0, network: 0 },
          throughput: 0,
          errorRate: 0
        }
      };

      (manager as any)._activeOrchestrations.set('test-cleanup', mockState);
      (manager as any)._workflowRegistry.set('test-workflow', {});
      (manager as any)._coordinationStrategies.set('test-strategy', {});
      (manager as any)._serviceRegistry.set('test-service', {});

      await manager.shutdown();

      // Verify cleanup
      expect((manager as any)._activeOrchestrations.size).toBe(0);
      expect((manager as any)._workflowRegistry.size).toBe(0);
      expect((manager as any)._coordinationStrategies.size).toBe(0);
      expect((manager as any)._serviceRegistry.size).toBe(0);
    });
  });

  // ============================================================================
  // SINGLETON FACTORY TESTS
  // ============================================================================

  describe('Singleton Factory', () => {
    test('should return same instance on multiple calls', () => {
      const instance1 = getGovernanceRuleOrchestrationManager();
      const instance2 = getGovernanceRuleOrchestrationManager();

      expect(instance1).toBe(instance2);
    });

    test('should create new instance with configuration', () => {
      // Reset singleton
      (getGovernanceRuleOrchestrationManager as any).orchestrationManagerInstance = null;

      const config = {
        service: {
          name: 'test-orchestration-manager',
          version: '1.0.0',
          environment: 'development' as const,
          timeout: 30000,
          retry: {
            maxAttempts: 3,
            delay: 1000,
            backoffMultiplier: 2,
            maxDelay: 10000
          }
        }
      };
      const instance = getGovernanceRuleOrchestrationManager(config);

      expect(instance).toBeDefined();
      expect(instance.id).toMatch(/governance-rule-orchestration-manager-\d+/);
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle initialization failure gracefully', async () => {
      const originalDoInitialize = (manager as any).doInitialize;
      (manager as any).doInitialize = jest.fn().mockRejectedValue(new Error('Initialization failed'));

      await expect(manager.initialize()).rejects.toThrow('Initialization failed');

      // Restore original method
      (manager as any).doInitialize = originalDoInitialize;
    });

    test('should handle shutdown errors gracefully', async () => {
      await manager.initialize();

      const originalCancelActiveOrchestrations = (manager as any)._cancelActiveOrchestrations;
      (manager as any)._cancelActiveOrchestrations = jest.fn().mockRejectedValue(new Error('Shutdown error'));

      await expect(manager.shutdown()).rejects.toThrow('Shutdown error');

      // Restore original method
      (manager as any)._cancelActiveOrchestrations = originalCancelActiveOrchestrations;
    });

    test('should handle workflow failure scenarios', async () => {
      await manager.initialize();

      const workflow = {
        id: 'failing-workflow',
        name: 'Failing Workflow',
        version: '1.0.0',
        description: 'Workflow that will fail',
        steps: [
          {
            id: 'failing-step',
            name: 'Failing Step',
            type: 'ACTION' as const,
            action: {
              type: 'service-call',
              service: 'non-existent-service',
              method: 'execute',
              parameters: {}
            },
            dependencies: [],
            rollbackAction: undefined,
            timeout: 5000,
            retryPolicy: {
              maxAttempts: 1,
              delay: 100,
              backoffStrategy: 'exponential',
              maxDelay: 1000
            },
            validation: {
              enabled: true,
              rules: [],
              onFailure: 'stop'
            },
            parameters: {}
          }
        ],
        conditions: [],
        rollbackStrategy: 'AUTOMATIC' as const,
        timeout: 30000,
        priority: 'HIGH' as const,
        tags: ['test', 'failing'],
        metadata: {
          category: 'test',
          author: 'test-suite',
          version: '1.0.0',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      };

      const context = {
        contextId: 'failing-context',
        user: {
          id: 'test-user',
          roles: ['test'],
          permissions: ['test:read', 'test:write']
        },
        environment: 'development' as const,
        technical: {
          version: '1.0.0',
          features: ['testing'],
          capabilities: ['workflow-execution']
        },
        custom: {
          testMode: true,
          source: 'unit-test'
        }
      };

      // Mock service call to fail
      const originalExecuteServiceCall = (manager as any)._executeServiceCall;
      (manager as any)._executeServiceCall = jest.fn().mockRejectedValue(new Error('Service not found'));

      await expect(manager.executeWorkflow(workflow, context)).rejects.toThrow();

      // Restore original method
      (manager as any)._executeServiceCall = originalExecuteServiceCall;
    });

    test('should handle condition evaluation edge cases', async () => {
      await manager.initialize();

      // Test condition evaluation with various scenarios
      const testCondition = { name: 'test-condition', expression: 'value === true', operator: 'equals' as const, value: true };
      const testContext = {
        contextId: 'test',
        userId: 'test',
        sessionId: 'test',
        requestId: 'test',
        timestamp: new Date(),
        environment: 'test',
        metadata: {},
        errorHandling: { onError: 'continue' as const, maxRetries: 3, retryDelay: 1000, rollbackSteps: [] }
      };

      const result = await (manager as any)._evaluateCondition(testCondition, testContext);
      expect(typeof result).toBe('boolean');
    });

    test('should handle resource calculation edge cases', async () => {
      await manager.initialize();

      const resourceUsage = await (manager as any)._calculateResourceUsage('test-orchestration');
      expect(resourceUsage).toBeDefined();
      expect(typeof resourceUsage.cpu).toBe('number');
      expect(typeof resourceUsage.memory).toBe('number');
      expect(typeof resourceUsage.network).toBe('number');
    });

    test('should handle metrics collection errors', async () => {
      await manager.initialize();

      // Mock performance metrics collection to fail
      const originalCollectPerformanceMetrics = (manager as any)._collectPerformanceMetrics;
      (manager as any)._collectPerformanceMetrics = jest.fn().mockRejectedValue(new Error('Metrics error'));

      // Should not throw error, just log it
      await (manager as any)._collectPerformanceMetrics();

      // Restore original method
      (manager as any)._collectPerformanceMetrics = originalCollectPerformanceMetrics;
    });
  });
});
