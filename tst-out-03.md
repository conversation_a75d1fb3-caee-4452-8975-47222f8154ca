npx jest server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts --coverage --collectCoverageFrom="server/src/platform/governance/advanced-management/GovernanceRuleDynamicProcessor.ts" --verbose --testTimeout=30000
  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts (541.213 s, 66 MB heap size)
  GovernanceRuleDynamicProcessor
    Constructor and Initialization
      ✓ should create processor with correct properties (8 ms)
      ✓ should initialize with default configuration (3 ms)
      ✓ should initialize resilient timing components (3 ms)
      ✓ should have proper configuration structure (3 ms)
    Dynamic Rule Processing
      ✕ should process dynamic rules successfully (30005 ms)
      ✓ should handle empty rules array (70 ms)
      ✓ should handle missing context (2 ms)
      ✓ should handle rule compilation errors (5 ms)
      ✕ should track performance metrics during processing (30007 ms)
      ✕ should apply adaptive learning when enabled (30014 ms)
      ✕ should generate optimization suggestions (30011 ms)
    Runtime Rule Modification
      ✕ should modify rule at runtime successfully (30004 ms)
      ✕ should handle missing rule ID (30005 ms)
      ✕ should handle missing modifications (30007 ms)
      ✕ should handle non-existent rule (30006 ms)
      ✕ should validate modifications before applying (30006 ms)
      ✕ should create backup version before modification (30005 ms)
      ✕ should handle different modification types (30006 ms)
    Rule Performance Optimization
      ✓ should optimize rule performance successfully (4 ms)
      ✓ should handle missing rule set (5 ms)
      ✓ should validate optimization results (3 ms)
    Rule Modification Validation
      ✓ should validate rule modification successfully (4 ms)
      ✓ should detect missing rule ID (3 ms)
      ✓ should detect missing modification type (2 ms)
      ✓ should detect empty modifications (2 ms)
      ✓ should validate approval requirements (2 ms)
      ✓ should handle validation errors gracefully (3 ms)
    Rule Rollback Operations
      ✕ should rollback rule changes successfully (30003 ms)
      ✕ should rollback to specific version (30005 ms)
      ✕ should handle missing rule ID (30004 ms)
      ✕ should handle rule without version history (30005 ms)
      ✕ should handle rollback validation (30004 ms)
    BaseTrackingService Interface
      ✓ should track data successfully (3 ms)
      ✓ should validate service state successfully (4 ms)
      ✓ should detect memory usage issues (2 ms)
      ✓ should handle validation errors gracefully (3 ms)
    IGovernanceService Interface
      ✓ should check if service is ready (3 ms)
      ✕ should get comprehensive metrics (4 ms)
      ✓ should handle metrics collection errors (3 ms)
    Memory Management and Cleanup
      ✕ should perform periodic optimization (30003 ms)
      ✓ should process modification queue (3 ms)
      ✓ should clean up expired data (2 ms)
      ✓ should update adaptive learning (3 ms)
      ✓ should handle cleanup errors gracefully (3 ms)
    Error Handling and Edge Cases
      ✕ should handle compilation cache overflow (2 ms)
      ✕ should handle rule execution timeout (4 ms)
      ✕ should handle memory pressure scenarios (30003 ms)
      ✓ should handle invalid rule configurations (4 ms)
    Performance and Metrics Calculation
      ✓ should calculate performance metrics correctly (3 ms)
      ✓ should handle empty rule sets in calculations (3 ms)
    Surgical Precision Coverage Tests
      ✓ should cover ternary operator branches in error handling (3 ms)
      ✓ should cover logging method branches (2 ms)
      ✓ should cover configuration branch conditions (2 ms)
      ✓ should cover null/undefined safety checks (4 ms)
      ✓ should cover private method access patterns (2 ms)
      ✕ should cover error path branches (3 ms)
      ✕ should cover metrics collection branches (3 ms)
      ✓ should cover initialization branches (3 ms)
      ✓ should cover validation helper methods (3 ms)
      ✓ should cover timing context branches (3 ms)
      ✓ should cover additional error handling branches (2 ms)
      ✓ should cover service lifecycle methods (3 ms)
      ✓ should cover configuration validation branches (3 ms)
      ✓ should cover metrics error handling (2 ms)
      ✓ should cover additional validation scenarios (2 ms)
      ✓ should achieve high coverage through comprehensive method testing (4 ms)
      ✓ should cover edge cases and boundary conditions (3 ms)
      ✓ should achieve 90%+ coverage through mock-based async testing (4 ms)
      ✓ should achieve coverage for modifyRuleAtRuntime through mocking (3 ms)
      ✓ should achieve coverage for optimizeRulePerformance through mocking (3 ms)

  ● GovernanceRuleDynamicProcessor › Dynamic Rule Processing › should process dynamic rules successfully

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      476 |
      477 |   describe('Dynamic Rule Processing', () => {
    > 478 |     test('should process dynamic rules successfully', async () => {
          |     ^
      479 |       const rules = [mockRule];
      480 |       const result = await processor.processDynamicRules(rules, mockContext);
      481 |

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:478:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:477:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Dynamic Rule Processing › should track performance metrics during processing

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      513 |     });
      514 |
    > 515 |     test('should track performance metrics during processing', async () => {
          |     ^
      516 |       const rules = [mockRule];
      517 |       const result = await processor.processDynamicRules(rules, mockContext);
      518 |

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:515:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:477:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Dynamic Rule Processing › should apply adaptive learning when enabled

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      523 |     });
      524 |
    > 525 |     test('should apply adaptive learning when enabled', async () => {
          |     ^
      526 |       const learningRule = createMockDynamicRule({
      527 |         adaptiveLearning: { learningEnabled: true, patternData: {}, adaptationCount: 0, lastAdaptation: new Date() }
      528 |       });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:525:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:477:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Dynamic Rule Processing › should generate optimization suggestions

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      534 |     });
      535 |
    > 536 |     test('should generate optimization suggestions', async () => {
          |     ^
      537 |       // Create rule with poor performance to trigger suggestions
      538 |       const slowRule = createMockDynamicRule({
      539 |         performanceMetrics: {

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:536:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:477:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Runtime Rule Modification › should modify rule at runtime successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      555 |
      556 |   describe('Runtime Rule Modification', () => {
    > 557 |     beforeEach(async () => {
          |     ^
      558 |       // Add a rule to modify
      559 |       await processor.processDynamicRules([mockRule], mockContext);
      560 |     });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:557:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:556:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Runtime Rule Modification › should handle missing rule ID

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      555 |
      556 |   describe('Runtime Rule Modification', () => {
    > 557 |     beforeEach(async () => {
          |     ^
      558 |       // Add a rule to modify
      559 |       await processor.processDynamicRules([mockRule], mockContext);
      560 |     });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:557:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:556:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Runtime Rule Modification › should handle missing modifications

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      555 |
      556 |   describe('Runtime Rule Modification', () => {
    > 557 |     beforeEach(async () => {
          |     ^
      558 |       // Add a rule to modify
      559 |       await processor.processDynamicRules([mockRule], mockContext);
      560 |     });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:557:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:556:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Runtime Rule Modification › should handle non-existent rule

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      555 |
      556 |   describe('Runtime Rule Modification', () => {
    > 557 |     beforeEach(async () => {
          |     ^
      558 |       // Add a rule to modify
      559 |       await processor.processDynamicRules([mockRule], mockContext);
      560 |     });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:557:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:556:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Runtime Rule Modification › should validate modifications before applying

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      555 |
      556 |   describe('Runtime Rule Modification', () => {
    > 557 |     beforeEach(async () => {
          |     ^
      558 |       // Add a rule to modify
      559 |       await processor.processDynamicRules([mockRule], mockContext);
      560 |     });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:557:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:556:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Runtime Rule Modification › should create backup version before modification

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      555 |
      556 |   describe('Runtime Rule Modification', () => {
    > 557 |     beforeEach(async () => {
          |     ^
      558 |       // Add a rule to modify
      559 |       await processor.processDynamicRules([mockRule], mockContext);
      560 |     });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:557:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:556:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Runtime Rule Modification › should handle different modification types

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      555 |
      556 |   describe('Runtime Rule Modification', () => {
    > 557 |     beforeEach(async () => {
          |     ^
      558 |       // Add a rule to modify
      559 |       await processor.processDynamicRules([mockRule], mockContext);
      560 |     });

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:557:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:556:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Rule Rollback Operations › should rollback rule changes successfully

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      788 |
      789 |   describe('Rule Rollback Operations', () => {
    > 790 |     beforeEach(async () => {
          |     ^
      791 |       // Setup rule with version history
      792 |       await processor.processDynamicRules([mockRule], mockContext);
      793 |       await processor.modifyRuleAtRuntime(mockRule.ruleId, mockModification);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:790:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:789:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Rule Rollback Operations › should rollback to specific version

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      788 |
      789 |   describe('Rule Rollback Operations', () => {
    > 790 |     beforeEach(async () => {
          |     ^
      791 |       // Setup rule with version history
      792 |       await processor.processDynamicRules([mockRule], mockContext);
      793 |       await processor.modifyRuleAtRuntime(mockRule.ruleId, mockModification);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:790:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:789:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Rule Rollback Operations › should handle missing rule ID

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      788 |
      789 |   describe('Rule Rollback Operations', () => {
    > 790 |     beforeEach(async () => {
          |     ^
      791 |       // Setup rule with version history
      792 |       await processor.processDynamicRules([mockRule], mockContext);
      793 |       await processor.modifyRuleAtRuntime(mockRule.ruleId, mockModification);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:790:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:789:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Rule Rollback Operations › should handle rule without version history

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      788 |
      789 |   describe('Rule Rollback Operations', () => {
    > 790 |     beforeEach(async () => {
          |     ^
      791 |       // Setup rule with version history
      792 |       await processor.processDynamicRules([mockRule], mockContext);
      793 |       await processor.modifyRuleAtRuntime(mockRule.ruleId, mockModification);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:790:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:789:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Rule Rollback Operations › should handle rollback validation

    thrown: "Exceeded timeout of 30000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      788 |
      789 |   describe('Rule Rollback Operations', () => {
    > 790 |     beforeEach(async () => {
          |     ^
      791 |       // Setup rule with version history
      792 |       await processor.processDynamicRules([mockRule], mockContext);
      793 |       await processor.modifyRuleAtRuntime(mockRule.ruleId, mockModification);

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:790:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:789:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › IGovernanceService Interface › should get comprehensive metrics

    expect(received).toBeDefined()

    Received: undefined

      915 |
      916 |       expect(metrics).toBeDefined();
    > 917 |       expect(metrics.serviceInfo).toBeDefined();
          |                                   ^
      918 |       expect(metrics.serviceInfo.id).toBe(processor.id);
      919 |       expect(metrics.serviceInfo.version).toBe('1.0.0');
      920 |       expect(metrics.serviceInfo.authority).toBe(processor.authority);

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:917:35)

  ● GovernanceRuleDynamicProcessor › Memory Management and Cleanup › should perform periodic optimization

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      945 |
      946 |   describe('Memory Management and Cleanup', () => {
    > 947 |     test('should perform periodic optimization', async () => {
          |     ^
      948 |       // Add some rules to optimize
      949 |       await processor.processDynamicRules([mockRule], mockContext);
      950 |

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:947:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:946:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Error Handling and Edge Cases › should handle compilation cache overflow

    expect(received).toBeLessThanOrEqual(expected)

    Matcher error: expected value must be a number or bigint

    Expected has value: undefined

      1026 |
      1027 |       // Cache should not exceed limit
    > 1028 |       expect((processor as any)._compilationCache.size).toBeLessThanOrEqual(maxCachedRules);
           |                                                         ^
      1029 |     });
      1030 |
      1031 |     test('should handle rule execution timeout', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:1028:57)

  ● GovernanceRuleDynamicProcessor › Error Handling and Edge Cases › should handle rule execution timeout

    expect(received).toBe(expected) // Object.is equality

    Expected: false
    Received: undefined

      1048 |       const result = await (processor as any)._executeRule(mockRule, timeoutContext);
      1049 |
    > 1050 |       expect(result.success).toBe(false);
           |                              ^
      1051 |       expect(result.error).toContain('timeout');
      1052 |
      1053 |       // Restore original method

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:1050:30)

  ● GovernanceRuleDynamicProcessor › Error Handling and Edge Cases › should handle memory pressure scenarios

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1055 |     });
      1056 |
    > 1057 |     test('should handle memory pressure scenarios', async () => {
           |     ^
      1058 |       const highMemoryContext = createMockDynamicContext({
      1059 |         runtimeEnvironment: {
      1060 |           memoryPressure: 0.9, // High memory pressure

      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:1057:5
      at server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:1018:3
      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:410:1)

  ● GovernanceRuleDynamicProcessor › Surgical Precision Coverage Tests › should cover error path branches

    expect(received).toBe(expected) // Object.is equality

    Expected: "invalid"
    Received: "valid"

      1228 |       const result = await processor.validateRuleModification(invalidModification);
      1229 |       expect(result).toBeDefined();
    > 1230 |       expect(result.status).toBe('invalid');
           |                             ^
      1231 |     });
      1232 |
      1233 |     test('should cover metrics collection branches', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:1230:29)

  ● GovernanceRuleDynamicProcessor › Surgical Precision Coverage Tests › should cover metrics collection branches

    expect(received).toBeDefined()

    Received: undefined

      1247 |
      1248 |       const ruleCache = (processor as any)._ruleCache;
    > 1249 |       expect(ruleCache).toBeDefined();
           |                         ^
      1250 |     });
      1251 |
      1252 |     test('should cover initialization branches', async () => {

      at Object.<anonymous> (server/src/platform/governance/advanced-management/__tests__/GovernanceRuleDynamicProcessor.test.ts:1249:25)

-----------------------------------|---------|----------|---------|---------|-------------------------------------------------------
File                               | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                     
-----------------------------------|---------|----------|---------|---------|-------------------------------------------------------
All files                          |   68.76 |    53.41 |   66.21 |    68.9 |                                                       
 GovernanceRuleDynamicProcessor.ts |   68.76 |    53.41 |   66.21 |    68.9 | ...-2010,2085-2123,2137,2154,2157,2160,2167-2188,2202 
-----------------------------------|---------|----------|---------|---------|-------------------------------------------------------
Test Suites: 1 failed, 1 total
Tests:       23 failed, 47 passed, 70 total
Snapshots:   0 total
Time:        1623.043 s
